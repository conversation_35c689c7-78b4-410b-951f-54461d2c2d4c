import 'package:board/Pages/My_page_View.dart';
import 'package:flutter/material.dart';
class FirstPage extends StatefulWidget {
  const FirstPage({super.key});

  @override
  State<FirstPage> createState() => _FirstPageState();
}

class _FirstPageState extends State<FirstPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Padding(
          padding: const EdgeInsets.only(top: 30),
          child: Image.asset(
            'images/App_bar_logo.png',
            height: 40,
            fit: BoxFit.contain,
          ),
        ),
        centerTitle: false,
        actions: [
          Padding(
            padding: const EdgeInsets.only(top: 30 , right: 20),
            child: Icon(Icons.notification_add , size: 30, color: Colors.redAccent,),
          )
        ],
      ),
      body:Expanded(child: Padding(
        padding: const EdgeInsets.only(top: 50 , left: 15),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 50, 
                  backgroundColor: const Color.fromARGB(255, 74, 183, 182),
                  child: Icon(Icons.location_on_outlined, 
                  size: 50, color: Colors.white,),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'طولكرم',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                        ),
                      ),
                      Text('طولكرم , فلسطين', style: TextStyle(
                        fontSize: 25, color: Colors.black, 
                        fontWeight: FontWeight.bold
                      ),),
                    ],
                  ),
                ),
                Spacer(), // This pushes the icon to the right
                Icon(Icons.chevron_right, size: 50)
              ],
            ) , 
            SizedBox(height: 20,) , 
           Padding(
             padding: const EdgeInsets.only(right: 15),
             child: Container(
              width: 400,height: 80,
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 232, 232, 232),
                borderRadius: BorderRadius.circular(10) ,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.5),
                    spreadRadius: 2,
                    blurRadius: 3,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Icon(Icons.search_rounded , size: 50,
                     color: const Color.fromARGB(255, 161, 164, 167),
                    ),
                    Text('ابحث عن منتجاتك المفضلة ... '  , 
                    style: TextStyle(
                      fontSize: 20 , 
                       color: const Color.fromARGB(255, 129, 131, 133),
                    ),) , 
                    Spacer() , 
                    SizedBox(
                      height: 40,
                      child: VerticalDivider(
                        color: const Color.fromARGB(255, 169, 168, 168),
                        thickness: 4,
                      width: 4,
                      ),
                    ) ,
                    Icon(Icons.mic_none_outlined , size: 50,
                     color: const Color.fromARGB(255, 74, 183, 182),
                    ) 
                  
                  ],
                ),
              ),
             ),
           ) ,
           Padding(
             padding: const EdgeInsets.only(top: 80),
             child: MyPageView(),
           ) 
          ],
        ),
      ))
    );
  }
}