# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 79ms
    [gap of 10ms]
    create-variant-model 51ms
    [gap of 11ms]
    create-ARMEABI_V7A-model 153ms
    create-ARM64_V8A-model 32ms
    create-X86-model 23ms
    create-X86_64-model 23ms
    create-module-model 15ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 19ms
    create-X86-model 21ms
    create-X86_64-model 20ms
    create-module-model 20ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 22ms
    create-X86-model 19ms
    create-X86_64-model 20ms
  create-initial-cxx-model completed in 651ms
  [gap of 71ms]
create_cxx_tasks completed in 740ms

