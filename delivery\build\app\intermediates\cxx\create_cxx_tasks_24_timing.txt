# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 32ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 60ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 16ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    [gap of 18ms]
  create-initial-cxx-model completed in 302ms
  [gap of 40ms]
create_cxx_tasks completed in 351ms

