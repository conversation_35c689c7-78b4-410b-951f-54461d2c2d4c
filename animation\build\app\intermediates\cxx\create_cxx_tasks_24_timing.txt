# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 85ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 70ms
    [gap of 39ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    [gap of 36ms]
  create-initial-cxx-model completed in 311ms
  [gap of 47ms]
create_cxx_tasks completed in 371ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 66ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 87ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 18ms
    [gap of 13ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
  create-initial-cxx-model completed in 365ms
  [gap of 45ms]
create_cxx_tasks completed in 424ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 58ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 55ms
    [gap of 45ms]
    create-ARM64_V8A-model 11ms
    create-X86_64-model 10ms
    [gap of 39ms]
  create-initial-cxx-model completed in 252ms
  [gap of 32ms]
create_cxx_tasks completed in 300ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 31ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 43ms
    [gap of 84ms]
  create-initial-cxx-model completed in 182ms
  [gap of 24ms]
create_cxx_tasks completed in 215ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 43ms
    [gap of 82ms]
  create-initial-cxx-model completed in 195ms
  [gap of 29ms]
create_cxx_tasks completed in 232ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 33ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 68ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 10ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 14ms
    [gap of 14ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 17ms]
  create-initial-cxx-model completed in 278ms
  [gap of 32ms]
create_cxx_tasks completed in 319ms

