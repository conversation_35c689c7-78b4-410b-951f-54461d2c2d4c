import 'package:board/shop.dart';
import 'package:flutter/material.dart';

class MyPageView extends StatefulWidget {

  MyPageView({super.key});

  @override
  State<MyPageView> createState() => _MyPageViewState();
}

class _MyPageViewState extends State<MyPageView> {
  // Set viewportFraction to less than 1.0 to create space between pages
  final PageController _pageController = PageController(viewportFraction: 0.60);

List<Sale_Photo> Prodacts = [
  Sale_Photo(image: 'images/tom.png', title: 'Fresh Tomatoes', prise: '25%', backgrownd_color: Colors.red),
  Sale_Photo(image: 'images/cum.png', title: 'cucumber', prise: '32%', backgrownd_color: Colors.green),
  Sale_Photo(image: 'images/orng.png', title: 'orange', prise: '10%', backgrownd_color: Colors.orange),
];
int _currentIndex = 0 ;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 650, height: 300,
      child: PageView.builder(
        controller: _pageController,
        itemCount: Prodacts.length,
         onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.0), // Add padding to each page
            child: My_Page(
              backgroundColor: Prodacts[index].backgrownd_color,
              image: Prodacts[index].image,
              title: Prodacts[index].title,
              prise: Prodacts[index].prise,
            ),
          );
        },
      ),
    );
  }
}

class My_Page extends StatelessWidget {
  final Color backgroundColor;
  final String image;
  final String title;
  final String prise;

  const My_Page({
    super.key,
    required this.backgroundColor,
    required this.image,
    required this.title,
    required this.prise,
  });

  @override
  Widget build(BuildContext context) {
    return  Container(
      width: 300, height: 250,
      child: Stack(
        children: [
         ClipRRect(
           borderRadius: BorderRadius.circular(12),
           child: Image.asset(image),
         ) , 
         Positioned(
            top: 10, left: 10,
            child: Container(
              width: 100, height: 30,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Text('$prise off', style: TextStyle(
                  fontSize: 18, fontWeight: FontWeight.bold
                ),),
              ),
            ),
          )
        ],
      ),
    );
  }
}
