# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 69ms
    create-variant-model 48ms
    create-ARMEABI_V7A-model 129ms
    create-ARM64_V8A-model 23ms
    create-X86-model 23ms
    create-X86_64-model 24ms
    create-module-model 14ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 21ms
    create-module-model 16ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 19ms
    create-X86-model 24ms
    [gap of 23ms]
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 590ms
  [gap of 60ms]
create_cxx_tasks completed in 669ms

